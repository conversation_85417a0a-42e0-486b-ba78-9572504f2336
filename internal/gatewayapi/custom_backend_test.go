// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package gatewayapi

import (
	"testing"

	"k8s.io/apimachinery/pkg/runtime/schema"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/ir"
)

func TestIsCustomBackendResource(t *testing.T) {
	tests := []struct {
		name               string
		extensionGroupKinds []schema.GroupKind
		group              *gwapiv1.Group
		kind               string
		expected           bool
	}{
		{
			name: "custom backend resource matches",
			extensionGroupKinds: []schema.GroupKind{
				{Group: "example.io", Kind: "S3Backend"},
				{Group: "aws.io", Kind: "LambdaBackend"},
			},
			group:    (*gwapiv1.Group)(stringPtr("example.io")),
			kind:     "S3Backend",
			expected: true,
		},
		{
			name: "custom backend resource matches with empty group",
			extensionGroupKinds: []schema.GroupKind{
				{Group: "", Kind: "CustomBackend"},
			},
			group:    nil,
			kind:     "CustomBackend",
			expected: true,
		},
		{
			name: "custom backend resource does not match kind",
			extensionGroupKinds: []schema.GroupKind{
				{Group: "example.io", Kind: "S3Backend"},
			},
			group:    (*gwapiv1.Group)(stringPtr("example.io")),
			kind:     "DifferentBackend",
			expected: false,
		},
		{
			name: "custom backend resource does not match group",
			extensionGroupKinds: []schema.GroupKind{
				{Group: "example.io", Kind: "S3Backend"},
			},
			group:    (*gwapiv1.Group)(stringPtr("different.io")),
			kind:     "S3Backend",
			expected: false,
		},
		{
			name:                "no extension group kinds configured",
			extensionGroupKinds: []schema.GroupKind{},
			group:               (*gwapiv1.Group)(stringPtr("example.io")),
			kind:                "S3Backend",
			expected:            false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translator := &Translator{
				ExtensionGroupKinds: tt.extensionGroupKinds,
			}

			result := translator.isCustomBackendResource(tt.group, tt.kind)
			if result != tt.expected {
				t.Errorf("isCustomBackendResource() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestProcessCustomBackendDestinationSetting(t *testing.T) {
	tests := []struct {
		name            string
		settingName     string
		backendRef      gwapiv1.BackendObjectReference
		backendNs       string
		protocol        ir.AppProtocol
		expectedName    string
		expectedProtocol ir.AppProtocol
	}{
		{
			name:        "custom backend destination setting",
			settingName: "custom-backend-setting",
			backendRef: gwapiv1.BackendObjectReference{
				Name: "s3-backend",
				Port: (*gwapiv1.PortNumber)(int32Ptr(80)),
			},
			backendNs:        "default",
			protocol:         ir.HTTP,
			expectedName:     "custom-backend-setting",
			expectedProtocol: ir.HTTP,
		},
		{
			name:        "custom backend with HTTPS protocol",
			settingName: "lambda-backend-setting",
			backendRef: gwapiv1.BackendObjectReference{
				Name: "lambda-backend",
				Port: (*gwapiv1.PortNumber)(int32Ptr(443)),
			},
			backendNs:        "aws-namespace",
			protocol:         ir.HTTPS,
			expectedName:     "lambda-backend-setting",
			expectedProtocol: ir.HTTPS,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translator := &Translator{}
			resources := &resource.Resources{}

			result := translator.processCustomBackendDestinationSetting(
				tt.settingName,
				tt.backendRef,
				tt.backendNs,
				tt.protocol,
				resources,
			)

			if result == nil {
				t.Fatal("processCustomBackendDestinationSetting() returned nil")
			}

			if result.Name != tt.expectedName {
				t.Errorf("Name = %v, expected %v", result.Name, tt.expectedName)
			}

			if result.Protocol != tt.expectedProtocol {
				t.Errorf("Protocol = %v, expected %v", result.Protocol, tt.expectedProtocol)
			}

			// Custom backends should have empty endpoints as they're processed by extensions
			if len(result.Endpoints) != 0 {
				t.Errorf("Endpoints should be empty for custom backends, got %d endpoints", len(result.Endpoints))
			}

			if result.Metadata == nil {
				t.Error("Metadata should not be nil")
			}
		})
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func int32Ptr(i int32) *int32 {
	return &i
}
