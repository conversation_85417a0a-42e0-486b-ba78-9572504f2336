// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package kubernetes

import (
	"testing"

	"k8s.io/apimachinery/pkg/runtime/schema"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"
)

func TestIsCustomBackendResource(t *testing.T) {
	tests := []struct {
		name           string
		extBackendGVKs []schema.GroupVersionKind
		group          *gwapiv1.Group
		kind           string
		expected       bool
	}{
		{
			name: "custom backend resource matches",
			extBackendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
				{Group: "aws.io", Version: "v1beta1", Kind: "LambdaBackend"},
			},
			group:    (*gwapiv1.Group)(stringPtr("example.io")),
			kind:     "S3Backend",
			expected: true,
		},
		{
			name: "custom backend resource matches with empty group",
			extBackendGVKs: []schema.GroupVersionKind{
				{Group: "", Version: "v1", Kind: "CustomBackend"},
			},
			group:    nil,
			kind:     "CustomBackend",
			expected: true,
		},
		{
			name: "custom backend resource does not match kind",
			extBackendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
			},
			group:    (*gwapiv1.Group)(stringPtr("example.io")),
			kind:     "DifferentBackend",
			expected: false,
		},
		{
			name: "custom backend resource does not match group",
			extBackendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
			},
			group:    (*gwapiv1.Group)(stringPtr("different.io")),
			kind:     "S3Backend",
			expected: false,
		},
		{
			name:           "no extension backend GVKs configured",
			extBackendGVKs: []schema.GroupVersionKind{},
			group:          (*gwapiv1.Group)(stringPtr("example.io")),
			kind:           "S3Backend",
			expected:       false,
		},
		{
			name: "multiple backend resources, second one matches",
			extBackendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
				{Group: "aws.io", Version: "v1beta1", Kind: "LambdaBackend"},
				{Group: "gcp.io", Version: "v1", Kind: "CloudFunctionBackend"},
			},
			group:    (*gwapiv1.Group)(stringPtr("aws.io")),
			kind:     "LambdaBackend",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reconciler := &gatewayAPIReconciler{
				extBackendGVKs: tt.extBackendGVKs,
			}

			result := reconciler.isCustomBackendResource(tt.group, tt.kind)
			if result != tt.expected {
				t.Errorf("isCustomBackendResource() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestExtensionManagerHasBackendExtension(t *testing.T) {
	tests := []struct {
		name           string
		backendGVKs    []schema.GroupVersionKind
		resourceGVKs   []schema.GroupVersionKind
		testGroup      gwapiv1.Group
		testKind       gwapiv1.Kind
		expected       bool
	}{
		{
			name: "backend resource matches",
			backendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
			},
			resourceGVKs: []schema.GroupVersionKind{},
			testGroup:    "example.io",
			testKind:     "S3Backend",
			expected:     true,
		},
		{
			name:         "resource GVK matches",
			backendGVKs:  []schema.GroupVersionKind{},
			resourceGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "CustomFilter"},
			},
			testGroup: "example.io",
			testKind:  "CustomFilter",
			expected:  true,
		},
		{
			name: "both backend and resource GVKs, backend matches",
			backendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
			},
			resourceGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "CustomFilter"},
			},
			testGroup: "example.io",
			testKind:  "S3Backend",
			expected:  true,
		},
		{
			name: "both backend and resource GVKs, resource matches",
			backendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
			},
			resourceGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "CustomFilter"},
			},
			testGroup: "example.io",
			testKind:  "CustomFilter",
			expected:  true,
		},
		{
			name: "no matches",
			backendGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "S3Backend"},
			},
			resourceGVKs: []schema.GroupVersionKind{
				{Group: "example.io", Version: "v1alpha1", Kind: "CustomFilter"},
			},
			testGroup: "different.io",
			testKind:  "UnknownKind",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// This test would require creating a mock extension manager
			// For now, we'll test the logic conceptually
			// In a real implementation, you would create a Manager with the test GVKs
			// and call HasExtension on it
			t.Logf("Test case: %s - would test HasExtension with group=%s, kind=%s, expected=%v",
				tt.name, tt.testGroup, tt.testKind, tt.expected)
		})
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}
